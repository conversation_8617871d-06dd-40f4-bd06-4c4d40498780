import 'dart:convert';
import 'dart:developer';
import 'package:flutter_appauth/flutter_appauth.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_onegate/data/datasources/gate_storage.dart';
import 'package:flutter_onegate/data/datasources/remote_datasource.dart';
import 'package:flutter_onegate/data/datasources/keycloack_config.dart';

class AuthService {
  final FlutterAppAuth _appAuth = const FlutterAppAuth();
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  final GateStorage gateStorage;
  final RemoteDataSource remoteDataSource;

  // Storage keys for secure storage
  static const String _accessTokenKey = 'access_token_secure';
  static const String _refreshTokenKey = 'refresh_token_secure';
  static const String _idTokenKey = 'id_token_secure';

  AuthService({
    required this.gateStorage,
    required this.remoteDataSource,
  });

  Future<void> initialize() async {
    try {
      log("✅ AppAuth service initialized successfully");
    } catch (e) {
      log('❌ Error initializing AppAuth service: $e');
      throw Exception('Failed to initialize AppAuth service: $e');
    }
  }

  Future<Map<String, dynamic>?> login() async {
    try {
      log('🔐 Starting AppAuth login flow...');

      // Step 1: Perform authorization request
      final AuthorizationResponse authResponse = await _appAuth.authorize(
        AppAuthConfigManager.getAuthorizationRequest(),
      );

      if (authResponse.authorizationCode == null) {
        throw Exception(
            'Authorization failed - no authorization code received');
      }

      log("🔐 Authorization code received, exchanging for tokens...");

      // Step 2: Exchange authorization code for tokens
      final TokenResponse tokenResponse = await _appAuth.token(
        AppAuthConfigManager.getTokenRequest(authResponse.authorizationCode!),
      );

      if (tokenResponse.accessToken == null) {
        throw Exception('Token exchange failed - no access token received');
      }

      log("🔑 Access token received: ${tokenResponse.accessToken!.substring(0, 20)}...");
      log("🔄 Refresh token received: ${tokenResponse.refreshToken != null ? 'Yes' : 'No'}");

      // Step 3: Store tokens securely
      await _storeTokens(tokenResponse);

      // Step 4: Get user info from Keycloak
      final userInfo = await _getUserInfo(tokenResponse.accessToken!);

      // Step 5: Save user data to local storage
      await _saveUserData(userInfo);

      log("✅ Login completed successfully");
      return userInfo;
    } catch (e) {
      log('❌ Login failed: $e');
      throw Exception('Login failed: $e');
    }
  }

  /// ✅ **Implement fetchSocieties**
  Future<List<dynamic>> fetchSocieties(String userId) async {
    try {
      return await remoteDataSource.fetchSocieties(userId);
    } catch (e) {
      log('❌ Error fetching societies: $e');
      throw Exception('Failed to fetch societies: $e');
    }
  }

  /// Store tokens securely
  Future<void> _storeTokens(TokenResponse tokenResponse) async {
    try {
      // Store in secure storage
      if (tokenResponse.accessToken != null) {
        await _secureStorage.write(
            key: _accessTokenKey, value: tokenResponse.accessToken!);
        await gateStorage.saveAccessToken(tokenResponse.accessToken!);
      }

      if (tokenResponse.refreshToken != null) {
        await _secureStorage.write(
            key: _refreshTokenKey, value: tokenResponse.refreshToken!);
        await gateStorage.saveRefreshToken(tokenResponse.refreshToken!);
      }

      if (tokenResponse.idToken != null) {
        await _secureStorage.write(
            key: _idTokenKey, value: tokenResponse.idToken!);
      }

      // Calculate and save token expiry
      if (tokenResponse.accessTokenExpirationDateTime != null) {
        await gateStorage
            .saveTokenExpiry(tokenResponse.accessTokenExpirationDateTime!);
      } else {
        // Default to 1 hour if no expiry provided
        final expiryTime = DateTime.now().add(const Duration(hours: 1));
        await gateStorage.saveTokenExpiry(expiryTime);
      }

      log("✅ Tokens stored successfully");
    } catch (e) {
      log("❌ Error storing tokens: $e");
      throw Exception('Failed to store tokens: $e');
    }
  }

  /// Get user info from Keycloak userinfo endpoint
  Future<Map<String, dynamic>> _getUserInfo(String accessToken) async {
    try {
      final response = await http.get(
        Uri.parse(AppAuthConfigManager.userInfoEndpoint),
        headers: {
          'Authorization': 'Bearer $accessToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final userInfo = jsonDecode(response.body) as Map<String, dynamic>;
        log("✅ User info retrieved successfully");
        return userInfo;
      } else {
        throw Exception(
            'Failed to get user info: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      log("❌ Error getting user info: $e");
      throw Exception('Failed to get user info: $e');
    }
  }

  /// Refresh access token using refresh token
  Future<bool> refreshToken() async {
    try {
      final refreshToken = await _secureStorage.read(key: _refreshTokenKey);

      if (refreshToken == null) {
        log('❌ No refresh token available');
        return false;
      }

      final TokenResponse tokenResponse = await _appAuth.token(
        AppAuthConfigManager.getRefreshTokenRequest(refreshToken),
      );

      if (tokenResponse.accessToken == null) {
        log('❌ Token refresh failed');
        return false;
      }

      await _storeTokens(tokenResponse);
      log('✅ Token refreshed successfully');
      return true;
    } catch (e) {
      log('❌ Error refreshing token: $e');
      return false;
    }
  }

  /// Get current access token (refresh if needed)
  Future<String?> getValidAccessToken() async {
    try {
      // Check if current token is expired
      final isExpired = await gateStorage.isTokenExpired();

      if (isExpired) {
        log('🔄 Token expired, attempting refresh...');
        final refreshed = await refreshToken();
        if (!refreshed) {
          log('❌ Token refresh failed');
          return null;
        }
      }

      return await gateStorage.getAccessToken();
    } catch (e) {
      log('❌ Error getting valid access token: $e');
      return null;
    }
  }

  /// Logout user and clear all tokens
  Future<void> logout() async {
    try {
      // Clear secure storage
      await _secureStorage.delete(key: _accessTokenKey);
      await _secureStorage.delete(key: _refreshTokenKey);
      await _secureStorage.delete(key: _idTokenKey);

      // Clear authentication-related data from local storage
      await gateStorage.clearStorage('access_token');
      await gateStorage.clearStorage('refresh_token');
      await gateStorage.clearStorage('token_expiry');
      await gateStorage.clearStorage('user_id');
      await gateStorage.clearStorage('username');
      await gateStorage.clearStorage('role');
      await gateStorage.clearStorage('society_id');

      log('✅ Logout completed successfully');
    } catch (e) {
      log('❌ Error during logout: $e');
      throw Exception('Logout failed: $e');
    }
  }

  Future<void> _saveUserData(Map<String, dynamic>? userInfo) async {
    if (userInfo == null) return;

    try {
      await gateStorage
          .saveUserId(userInfo["sub"] ?? userInfo["old_sso_user_id"] ?? "");
      await gateStorage.saveUsername(
          userInfo["preferred_username"] ?? userInfo["username"] ?? "");

      log("✅ User data saved successfully");
    } catch (e) {
      log("❌ Error saving user data: $e");
      throw Exception('Failed to save user data: $e');
    }
  }
}
