import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_appauth/flutter_appauth.dart';

class AppAuthConfigManager {
  static const String bundleIdentifier = 'com.cubeonebiz.gate';
  static const String clientId = 'onegate-sso';
  static const String frontendUrl = 'https://stgsso.cubeone.in';
  static const String realm = 'fstech';
  static const String clientSecret = 'zXpmFL8WzkDoL379FesFl2pgm8vxPa58';

  // OAuth/OIDC endpoints
  static String get authorizationEndpoint =>
      '$frontendUrl/realms/$realm/protocol/openid-connect/auth';
  static String get tokenEndpoint =>
      '$frontendUrl/realms/$realm/protocol/openid-connect/token';
  static String get userInfoEndpoint =>
      '$frontendUrl/realms/$realm/protocol/openid-connect/userinfo';
  static String get endSessionEndpoint =>
      '$frontendUrl/realms/$realm/protocol/openid-connect/logout';

  // Redirect URIs
  static String get redirectUrl => '$bundleIdentifier://login-callback';
  static String get postLogoutRedirectUrl =>
      '$bundleIdentifier://logout-callback';

  // Scopes
  static const List<String> scopes = ['openid', 'profile', 'email'];

  static AuthorizationServiceConfiguration getServiceConfiguration() {
    // Allow self-signed certificates in debug mode
    if (kDebugMode) {
      HttpOverrides.global = MyHttpOverrides();
    }

    return AuthorizationServiceConfiguration(
      authorizationEndpoint: authorizationEndpoint,
      tokenEndpoint: tokenEndpoint,
      endSessionEndpoint: endSessionEndpoint,
    );
  }

  static AuthorizationRequest getAuthorizationRequest() {
    return AuthorizationRequest(
      clientId,
      redirectUrl,
      serviceConfiguration: getServiceConfiguration(),
      scopes: scopes,
      additionalParameters: {
        'access_type': 'offline',
      },
    );
  }

  static TokenRequest getTokenRequest(String authorizationCode) {
    return TokenRequest(
      clientId,
      redirectUrl,
      authorizationCode: authorizationCode,
      serviceConfiguration: getServiceConfiguration(),
      clientSecret: clientSecret,
    );
  }

  static TokenRequest getRefreshTokenRequest(String refreshToken) {
    return TokenRequest(
      clientId,
      redirectUrl,
      refreshToken: refreshToken,
      serviceConfiguration: getServiceConfiguration(),
      clientSecret: clientSecret,
    );
  }
}

// Custom HTTP overrides to accept all certificates in debug mode
class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

// Legacy compatibility - will be removed after migration
class KeycloakConfigManager {
  static const String bundleIdentifier = AppAuthConfigManager.bundleIdentifier;
  static const String clientId = AppAuthConfigManager.clientId;
  static const String frontendUrl = AppAuthConfigManager.frontendUrl;
  static const String realm = AppAuthConfigManager.realm;
  static const String clientSecret = AppAuthConfigManager.clientSecret;

  @Deprecated("Use AppAuthConfigManager instead")
  static KeycloakConfigLegacy getConfig() {
    return const KeycloakConfigLegacy(
      bundleIdentifier: bundleIdentifier,
      clientId: clientId,
      frontendUrl: frontendUrl,
      realm: realm,
      clientSecret: clientSecret,
    );
  }
}

// Legacy config class for backward compatibility
class KeycloakConfigLegacy {
  final String bundleIdentifier;
  final String clientId;
  final String frontendUrl;
  final String realm;
  final String clientSecret;

  const KeycloakConfigLegacy({
    required this.bundleIdentifier,
    required this.clientId,
    required this.frontendUrl,
    required this.realm,
    required this.clientSecret,
  });
}
