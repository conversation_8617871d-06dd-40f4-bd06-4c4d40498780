import 'dart:io';
import 'dart:developer' as dev;
import 'package:flutter/foundation.dart';
import 'package:flutter_appauth/flutter_appauth.dart';

class AppAuthConfigManager {
  static const String bundleIdentifier = 'com.cubeonebiz.gate';
  static const String clientId = 'onegate-sso';
  static const String frontendUrl = 'https://stgsso.cubeone.in';
  static const String realm = 'fstech';
  static const String clientSecret = 'zXpmFL8WzkDoL379FesFl2pgm8vxPa58';

  // OAuth/OIDC endpoints
  static String get authorizationEndpoint =>
      '$frontendUrl/realms/$realm/protocol/openid-connect/auth';
  static String get tokenEndpoint =>
      '$frontendUrl/realms/$realm/protocol/openid-connect/token';
  static String get userInfoEndpoint =>
      '$frontendUrl/realms/$realm/protocol/openid-connect/userinfo';
  static String get endSessionEndpoint =>
      '$frontendUrl/realms/$realm/protocol/openid-connect/logout';

  // Redirect URIs
  static String get redirectUrl => '$bundleIdentifier://login-callback';
  static String get postLogoutRedirectUrl =>
      '$bundleIdentifier://logout-callback';

  // Scopes
  static const List<String> scopes = ['openid', 'profile', 'email'];

  /// Debug logging method to display all configuration details
  static void logClientConfiguration({bool includeSecret = false}) {
    dev.log('🔐 ===== KEYCLOAK CLIENT CONFIGURATION DEBUG =====');
    dev.log('📋 Client Details:');
    dev.log('   • Client ID: $clientId');
    dev.log('   • Bundle Identifier: $bundleIdentifier');
    dev.log('   • Frontend URL: $frontendUrl');
    dev.log('   • Realm: $realm');

    if (includeSecret) {
      dev.log(
          '   • Client Secret: ${clientSecret.isNotEmpty ? "${clientSecret.substring(0, 8)}..." : "NOT SET"}');
      dev.log(
          '   • Client Type: ${clientSecret.isNotEmpty ? "CONFIDENTIAL" : "PUBLIC"}');
    } else {
      dev.log('   • Client Secret: [HIDDEN - set includeSecret=true to show]');
      dev.log(
          '   • Client Type: ${clientSecret.isNotEmpty ? "CONFIDENTIAL" : "PUBLIC"}');
    }

    dev.log('🌐 OAuth/OIDC Endpoints:');
    dev.log('   • Authorization: $authorizationEndpoint');
    dev.log('   • Token: $tokenEndpoint');
    dev.log('   • UserInfo: $userInfoEndpoint');
    dev.log('   • End Session: $endSessionEndpoint');

    dev.log('🔗 Redirect URIs:');
    dev.log('   • Login Callback: $redirectUrl');
    dev.log('   • Logout Callback: $postLogoutRedirectUrl');

    dev.log('🔑 OAuth Scopes: ${scopes.join(", ")}');
    dev.log('🔐 ============================================');
  }

  static AuthorizationServiceConfiguration getServiceConfiguration() {
    // Allow self-signed certificates in debug mode
    if (kDebugMode) {
      HttpOverrides.global = MyHttpOverrides();
    }

    return AuthorizationServiceConfiguration(
      authorizationEndpoint: authorizationEndpoint,
      tokenEndpoint: tokenEndpoint,
      endSessionEndpoint: endSessionEndpoint,
    );
  }

  static AuthorizationRequest getAuthorizationRequest() {
    dev.log('🚀 Creating Authorization Request:');
    dev.log('   • Client ID: $clientId');
    dev.log('   • Redirect URL: $redirectUrl');
    dev.log('   • Scopes: ${scopes.join(", ")}');
    dev.log('   • Additional Parameters: access_type=offline');
    dev.log('   • Prompt Values: login');
    dev.log('   • PKCE: Enabled (handled automatically by flutter_appauth)');

    return AuthorizationRequest(
      clientId,
      redirectUrl,
      serviceConfiguration: getServiceConfiguration(),
      scopes: scopes,
      additionalParameters: {
        'access_type': 'offline',
      },
      // Enable PKCE for security
      promptValues: ['login'],
    );
  }

  static TokenRequest getTokenRequest(String authorizationCode,
      {String? codeVerifier}) {
    dev.log('🔄 Creating Token Request:');
    dev.log('   • Client ID: $clientId');
    dev.log('   • Redirect URL: $redirectUrl');
    dev.log(
        '   • Authorization Code: ${authorizationCode.substring(0, 10)}...');
    dev.log(
        '   • Code Verifier: ${codeVerifier != null ? "${codeVerifier.substring(0, 10)}..." : "NOT PROVIDED"}');
    dev.log('   • Client Secret: NOT INCLUDED (Public Client Configuration)');
    dev.log('   • Client Type: PUBLIC (PKCE-enabled)');

    return TokenRequest(
      clientId,
      redirectUrl,
      authorizationCode: authorizationCode,
      serviceConfiguration: getServiceConfiguration(),
      // For PKCE, don't include client secret for public clients
      // clientSecret: clientSecret,
      codeVerifier: codeVerifier,
    );
  }

  static TokenRequest getRefreshTokenRequest(String refreshToken) {
    return TokenRequest(
      clientId,
      redirectUrl,
      refreshToken: refreshToken,
      serviceConfiguration: getServiceConfiguration(),
      // For PKCE, don't include client secret for public clients
      // clientSecret: clientSecret,
    );
  }
}

// Custom HTTP overrides to accept all certificates in debug mode
class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

// Legacy compatibility - will be removed after migration
class KeycloakConfigManager {
  static const String bundleIdentifier = AppAuthConfigManager.bundleIdentifier;
  static const String clientId = AppAuthConfigManager.clientId;
  static const String frontendUrl = AppAuthConfigManager.frontendUrl;
  static const String realm = AppAuthConfigManager.realm;
  static const String clientSecret = AppAuthConfigManager.clientSecret;

  @Deprecated("Use AppAuthConfigManager instead")
  static KeycloakConfigLegacy getConfig() {
    return const KeycloakConfigLegacy(
      bundleIdentifier: bundleIdentifier,
      clientId: clientId,
      frontendUrl: frontendUrl,
      realm: realm,
      clientSecret: clientSecret,
    );
  }
}

// Legacy config class for backward compatibility
class KeycloakConfigLegacy {
  final String bundleIdentifier;
  final String clientId;
  final String frontendUrl;
  final String realm;
  final String clientSecret;

  const KeycloakConfigLegacy({
    required this.bundleIdentifier,
    required this.clientId,
    required this.frontendUrl,
    required this.realm,
    required this.clientSecret,
  });
}
